# 🚀 微信公众号批量阅读量抓取系统

专门用于批量抓取微信公众号文章阅读量、点赞数、分享数等统计数据的Python工具。

## ✨ 核心功能

- 🔍 **批量获取文章链接** - 自动获取公众号的文章列表
- 📊 **抓取阅读量统计** - 获取每篇文章的阅读量、点赞数、分享数等指标
- 💾 **智能数据保存** - 支持Excel和JSON格式导出
- 🛡️ **智能频率控制** - 避免触发微信反爬虫机制
- 🔧 **反爬虫检测** - 自动检测验证码页面并给出处理建议

## 🛠️ 核心文件

- `main_enhanced.py` - 主程序入口，用户界面
- `batch_readnum_spider.py` - 批量阅读量抓取核心
- `read_cookie.py` - Cookie提取和管理
- `wxCrawler.py` - 基础文章链接抓取
- `enhanced_wx_crawler.py` - 增强版文章抓取器
- `utils.py` - 工具函数
- `diagnose.py` - 系统诊断工具

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 使用步骤

```bash
python main_enhanced.py
# 1. 选择功能1：抓取Cookie
# 2. 选择功能3：批量阅读量统计抓取
```

### 3. 推荐参数

- **最大页数**: 1页（避免触发反爬虫）
- **每页文章数**: 1-2篇
- **抓取天数**: 7天
- **延迟设置**: 文章间30-60秒

## 📊 数据输出

数据保存在 `./data/readnum_batch/` 目录：

- Excel格式：`readnum_batch_YYYYMMDD_HHMMSS.xlsx`
- JSON格式：`readnum_batch_YYYYMMDD_HHMMSS.json`

## ⚠️ 重要提醒

### 使用限制

- **小批量使用** - 避免大规模抓取
- **频率控制** - 建议每次使用间隔1-2小时
- **Cookie更新** - 需要定期重新抓取Cookie

### 反爬虫处理

系统会自动检测微信验证码页面：

```
⚠️ 遇到微信验证码页面，需要手动验证
💡 建议：降低抓取频率，增加延迟时间
```

## 🔧 故障排除

### 阅读量显示为0

- **原因**: 触发微信反爬虫机制
- **解决**: 降低抓取频率，手动完成验证

### Cookie过期

```bash
python main_enhanced.py
# 选择功能1重新抓取Cookie
```

## 📄 许可证

仅供学习研究使用，请遵守相关法律法规和平台服务协议。
