import uiautomation as auto
import time
import sys

def find_gzh_control(window_control, gzh_name: str, timeout=10):
    """
    在指定窗口中查找公众号控件，采用带超时的重试机制和两步查找策略。

    Args:
        window_control: 目标窗口的 uiautomation 控件。
        gzh_name: 要查找的公众号名称。
        timeout: 查找超时时间（秒）。

    Returns:
        找到的可点击子控件，如果未找到则返回 None。
    """
    print(f"开始查找公众号控件: '{gzh_name}'...")
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # 第一步：使用正则表达式查找包含公众号名称和“公众号”字样的父容器
            parent_container = window_control.Control(
                searchDepth=20,
                RegexName=f"(?=.*{gzh_name})(?=.*公众号)"
            )

            if parent_container.Exists(0.1, 0.1):
                print("成功找到父容器，正在查找可点击的子控件...")
                # 第二步：在父容器中查找名称完全匹配的可点击子控件
                target_control = parent_container.ClickableControl(Name=gzh_name)
                
                if target_control.Exists(0.1, 0.1):
                    print(f"成功找到目标控件: '{target_control.Name}'")
                    return target_control
        except Exception as e:
            # 捕获查找过程中可能出现的异常，例如控件消失
            print(f"查找过程中出现异常: {e}，继续尝试...")
        
        time.sleep(0.5) # 每次尝试后短暂等待

    print(f"超时 {timeout} 秒，未能找到公众号 '{gzh_name}' 的控件。")
    return None

def search_and_open_article(account_name: str):
    """
    自动化操作微信，搜索指定公众号并点击进入。
    """
    print("正在查找微信窗口...")
    wechat_win = auto.WindowControl(searchDepth=1, ClassName='WeChatMainWndForPC')

    if not wechat_win.Exists(5, 1):
        print("未找到微信窗口，请确认微信已登录并打开。")
        return

    print(f"成功链接到微信窗口: {wechat_win.Name}")
    wechat_win.SetActive()
    time.sleep(1)

    print("点击搜索框...")
    search_box = wechat_win.EditControl(Name='搜索')
    if not search_box.Exists(3, 1):
        print("未找到搜索框。")
        return
    search_box.Click(simulateMove=False)
    time.sleep(0.5)
    search_box.SendKeys('{Ctrl}a{Delete}', interval=0.1)
    time.sleep(0.5)

    print(f"输入搜索内容：{account_name}")
    search_box.SendKeys(account_name, interval=0.1)
    time.sleep(1.5)
    search_box.SendKeys('{Enter}')
    time.sleep(3)

    print("等待搜索结果页面加载...")
    
    # 在当前窗口查找公众号控件
    gzh_control = find_gzh_control(wechat_win, account_name)

    if gzh_control:
        print(f"成功找到公众号 '{account_name}'，正在执行点击操作...")
        gzh_control.Click(simulateMove=False)
        print("点击操作完成。")
    else:
        print(f"最终未能找到公众号 '{account_name}' 的控件。")

    print("\n自动化操作执行完毕。")


if __name__ == '__main__':
    # ==================================================
    #  要操作的公众号名称，可以通过命令行参数传入，否则使用默认值
    if len(sys.argv) > 1:
        target_account = sys.argv[1]
        print(f"从命令行获取公众号名称: {target_account}")
    else:
        target_account = "南京发布"
        print(f"使用默认公众号名称: {target_account}")
    # ==================================================
    
    search_and_open_article(target_account)