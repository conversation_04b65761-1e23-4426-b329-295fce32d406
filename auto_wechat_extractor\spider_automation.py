import uiautomation as auto
import time
import sys



def search_and_open_article(account_name: str, timeout=15):
    """
    自动化操作微信，搜索指定公众号并点击进入。
    采用更通用的查找策略，以适应微信版本变化。
    """
    print("正在查找微信窗口...")
    wechat_win = auto.WindowControl(searchDepth=1, ClassName='WeChatMainWndForPC')

    if not wechat_win.Exists(5, 1):
        print("错误：未找到微信窗口。请确保微信已登录并显示在主界面。")
        return

    print(f"成功链接到微信窗口: {wechat_win.Name}")
    wechat_win.SetActive()
    time.sleep(1)

    print("步骤 1: 点击并清空搜索框...")
    search_box = wechat_win.EditControl(Name='搜索')
    if not search_box.Exists(3, 1):
        print("错误：未找到搜索框。")
        return
    search_box.Click(simulateMove=False)
    time.sleep(0.5)
    search_box.SendKeys('{Ctrl}a{Delete}', interval=0.1)
    time.sleep(0.5)

    print(f"步骤 2: 输入搜索内容 '{account_name}' 并回车...")
    search_box.SendKeys(account_name, interval=0.1)
    time.sleep(1.5)
    search_box.SendKeys('{Enter}')
    time.sleep(3) # 等待搜索结果加载

    print("步骤 3: 在搜索结果中查找公众号...")
    # === 新的通用查找策略 ===
    # 查找一个同时包含公众号名称和“公众号”字样的控件。
    # 这是最稳健的方法，因为它不依赖于控件的具体类型或层级。
    gzh_target = wechat_win.Control(
        searchDepth=15, # 在窗口内进行合理深度的搜索
        RegexName=f".*{account_name}.*公众号.*" # 正则表达式：匹配任何包含名称和“公众号”字样的控件
    )

    start_time = time.time()
    while time.time() - start_time < timeout:
        if gzh_target.Exists(0.5, 0.1):
            print(f"成功找到公众号 '{account_name}' 的目标区域！")
            try:
                gzh_target.Click(simulateMove=False, waitTime=1)
                print("步骤 4: 点击操作完成。")
                return # 成功找到并点击后，退出函数
            except Exception as e:
                print(f"尝试点击时发生错误: {e}，可能是控件状态已改变。")
                break # 发生错误则终止尝试
        else:
            print(f"未能立即找到，正在重试... (已用时 {int(time.time() - start_time)}s)")
    
    print(f"!!! 最终失败：在 {timeout} 秒内未能找到公众号 '{account_name}'。")
    print("建议：请检查公众号名称是否正确，或确认搜索结果页面是否已正常加载。")

    print("\n自动化操作执行完毕。")


if __name__ == '__main__':
    # ==================================================
    #  从配置文件 accounts.txt 中读取要操作的公众号列表
    try:
        with open('accounts.txt', 'r', encoding='utf-8') as f:
            target_accounts = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        print("错误：未找到公众号列表文件 'accounts.txt'。")
        print("请创建一个 accounts.txt 文件，每行输入一个公众号名称。")
        exit()

    if not target_accounts:
        print("'accounts.txt' 文件为空，请输入至少一个公众号名称。")
        exit()

    print(f"成功读取到 {len(target_accounts)} 个公众号，准备开始操作...")
    # ==================================================

    for account in target_accounts:
        print(f"\n{'='*20} 正在处理公众号: {account} {'='*20}")
        search_and_open_article(account)
        # 在处理下一个公众号之前，可以加入一个延时，给微信一些反应时间
        # 例如，等待5秒，让公众号页面完全加载，或者返回到主界面
        print("...等待 5 秒后继续下一个任务...")
        time.sleep(5)

    print(f"\n{'='*20} 所有公众号处理完毕 {'='*20}")