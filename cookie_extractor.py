import json
import re
import winreg
import atexit
from datetime import datetime
from mitmproxy import http

class WechatCookieExtractor:
    def __init__(self):
        self.keys_file = "wechat_keys.txt"
        self.saved_cookies = set()  # 用于去重的集合
        self.init_keys_file()
        self.set_system_proxy()
        # 注册程序退出时的清理函数
        atexit.register(self.cleanup_proxy)
        
    def init_keys_file(self):
        """初始化keys文件"""
        with open(self.keys_file, "w", encoding="utf-8") as f:
            f.write("=== 微信公众号Keys和URLs记录 ===\n")
            f.write(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    
    def set_system_proxy(self):
        """设置系统代理为127.0.0.1:8080"""
        try:
            # 打开注册表项
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                               r"Software\Microsoft\Windows\CurrentVersion\Internet Settings", 
                               0, winreg.KEY_WRITE)
            
            # 设置代理服务器
            winreg.SetValueEx(key, "ProxyServer", 0, winreg.REG_SZ, "127.0.0.1:8080")
            
            # 启用代理
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 1)
            
            # 关闭注册表项
            winreg.CloseKey(key)
            
            print("系统代理已设置为 127.0.0.1:8080")
            
        except Exception as e:
            print(f"设置系统代理失败: {e}")
    
    def tls_clienthello(self, data):
        """处理TLS握手，忽略非微信域名的证书错误"""
        # 只对微信相关域名进行SSL拦截
        wechat_domains = [
            "mp.weixin.qq.com",
            "weixin.qq.com", 
            "wx.qq.com",
            "api.weixin.qq.com"
        ]
        
        # 如果不是微信域名，不进行SSL拦截
        if not any(domain in str(data.context.server.address) for domain in wechat_domains):
            return
        
    def request(self, flow: http.HTTPFlow) -> None:
        """拦截请求，提取微信相关的Cookie和URL"""
        request = flow.request
        
        # 检查是否为微信公众号相关请求
        if self.is_wechat_request(request):
            self.save_keys_and_url(request)
    
    def is_wechat_request(self, request) -> bool:
        """判断是否为微信公众号相关请求"""
        wechat_domains = [
            "mp.weixin.qq.com",
            "weixin.qq.com", 
            "wx.qq.com"
        ]
        
        return any(domain in request.pretty_host for domain in wechat_domains)
    
    def save_keys_and_url(self, request):
        """保存Cookie、URL和关键Headers到统一文件，避免重复记录"""
        # 过滤掉jsmonitor等监控请求
        if "jsmonitor" in request.pretty_url:
            return

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 提取并合并所有关键Cookie为一行
        cookies_string = ""
        if request.cookies:
            cookie_parts = []
            key_cookies = ["session_key", "uin", "skey", "p_skey", "wxuin", "data_bizuin", "appmsg_token", "pass_ticket", "wap_sid2"]

            for cookie_name, cookie_value in request.cookies.items():
                if any(key in cookie_name.lower() for key in key_cookies) or len(cookie_value) > 20:
                    cookie_parts.append(f"{cookie_name}={cookie_value}")

            if cookie_parts:
                cookies_string = "; ".join(cookie_parts)

        # 提取关键的请求头参数（参考spider_readnum.py中的成功实现）
        key_headers = {}
        important_headers = [
            'x-wechat-key', 'x-wechat-uin', 'exportkey',
            'user-agent', 'accept', 'accept-language',
            'cache-control', 'sec-fetch-site', 'sec-fetch-mode',
            'sec-fetch-dest', 'priority'
        ]

        for header_name in important_headers:
            if header_name in request.headers:
                key_headers[header_name] = request.headers[header_name]

        # 如果没有cookie或cookie已经记录过，则不保存
        if not cookies_string or cookies_string in self.saved_cookies:
            return

        # 添加到已保存的集合中
        self.saved_cookies.add(cookies_string)

        with open(self.keys_file, "a", encoding="utf-8") as f:
            f.write(f"{'='*60}\n")
            f.write(f"time: {timestamp}\n")
            f.write(f"allurl: {request.pretty_url}\n")
            f.write(f"Cookies: {cookies_string}\n")

            # 保存关键的请求头参数
            if key_headers:
                f.write("Headers:\n")
                for header_name, header_value in key_headers.items():
                    f.write(f"  {header_name}: {header_value}\n")

            f.write("\n")
    
    def cleanup_proxy(self):
        """清理系统代理设置"""
        try:
            # 打开注册表项
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, 
                               r"Software\Microsoft\Windows\CurrentVersion\Internet Settings", 
                               0, winreg.KEY_WRITE)
            
            # 禁用代理
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 0)
            
            # 清空代理服务器设置（可选）
            winreg.SetValueEx(key, "ProxyServer", 0, winreg.REG_SZ, "")
            
            # 关闭注册表项
            winreg.CloseKey(key)
            
            print("系统代理已关闭")
            
        except Exception as e:
            print(f"关闭系统代理失败: {e}")

# 创建实例供mitmproxy使用
addons = [WechatCookieExtractor()]