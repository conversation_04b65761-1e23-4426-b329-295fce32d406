import mitmproxy.http
import json
import time
from urllib.parse import urlparse, parse_qs

class ExtractSetCookie:
    def __init__(self):
        self.credentials = {}

    def _generate_requests_code(self, request_data):
        """将请求数据转换为 Python requests 代码"""
        method = request_data['method']
        base_url = request_data['base_url']
        params = request_data['params']
        headers = request_data['headers']
        body = request_data.get('body')

        # 移除 requests 会自动处理或可能引起冲突的请求头
        headers_to_remove = ['Host', 'Content-Length', 'Accept-Encoding', 'Connection']
        for h in headers_to_remove:
            headers.pop(h, None)

        code = "import requests\n"
        code += "import json\n\n"
        code += f"url = \"{base_url}\"\n\n"

        code += f"params = {json.dumps(params, indent=4)}\n\n"

        code += "headers = {\n"
        for key, value in headers.items():
            # 对值中的反斜杠和引号进行转义
            escaped_value = value.replace('\\', '\\\\').replace("'", "\\'")
            code += f"    '{key}': '{escaped_value}',\n"
        code += "}\n\n"

        payload_variable = "data"
        if body:
            try:
                # 尝试将 body 解析为 JSON，如果成功则使用 json 参数
                json_body = json.loads(body)
                code += f"payload = {json.dumps(json_body, indent=4)}\n\n"
                payload_variable = "json"
            except json.JSONDecodeError:
                # 否则作为原始数据处理
                code += f"payload = \"\"\"{body}\"\"\"\n\n"
        else:
            code += "payload = {}\n\n"


        if payload_variable == "json":
             code += f"response = requests.request(\"{method}\", url, params=params, headers=headers, json=payload)\n\n"
        else:
             code += f"response = requests.request(\"{method}\", url, params=params, headers=headers, data=payload)\n\n"

        code += "print(response.status_code)\n"
        code += "print(response.text)\n"
        return code

    def response(self, flow: mitmproxy.http.HTTPFlow):
        # 检查请求的 URL 是否符合过滤器
        if flow.request.url.startswith("https://mp.weixin.qq.com/s?__biz="):
            # 解析 URL
            parsed_url = urlparse(flow.request.url)
            base_url = f"{parsed_url.scheme}://{parsed_url.netloc}{parsed_url.path}"
            query_params = parse_qs(parsed_url.query)
            
            # 提取 __biz 参数作为标识
            biz = query_params.get('__biz', [None])[0]
            if biz:
                # 提取响应头中的 Set-Cookie 数据
                set_cookie_header = flow.response.headers.get("Set-Cookie")
                if set_cookie_header:
                    timestamp = int(time.time() * 1000)
                    
                    # 存储凭据信息
                    self.credentials[biz] = {
                        "base_url": base_url,
                        "params": query_params,
                        "headers": dict(flow.request.headers),
                        "set_cookie": set_cookie_header,
                        "timestamp": timestamp,
                    }
                    # 将凭据数据保存到文件中
                    with open("credentials.json", "w", encoding="utf-8") as file:
                        json.dump(list(self.credentials.values()), file, indent=4, ensure_ascii=False)
                    
                    print(f"已捕获到公众号 (biz={biz}) 的凭据并保存到 credentials.json")

                    request_data = {
                        "method": flow.request.method,
                        "base_url": base_url,
                        "params": query_params,
                        "headers": dict(flow.request.headers),
                        "body": flow.request.get_content().decode("utf-8", errors="ignore"),
                    }

                    # 生成并保存 requests 代码
                    requests_code = self._generate_requests_code(request_data)
                    with open("generated_request.py", "w", encoding="utf-8") as code_file:
                        code_file.write(requests_code)
                    print("Python requests 代码已生成到 generated_request.py")


                    # 记录完整的请求和响应
                    log_entry = {
                        "timestamp": timestamp,
                        "request": request_data,
                        "response": {
                            "status_code": flow.response.status_code,
                            "headers": dict(flow.response.headers),
                            "body": flow.response.get_content().decode("utf-8", errors="ignore"),
                        },
                    }
                    with open("requests_log.json", "a", encoding="utf-8") as log_file:
                        log_file.write(json.dumps(log_entry, ensure_ascii=False, indent=4) + "\n")

addons = [
    ExtractSetCookie(),
]