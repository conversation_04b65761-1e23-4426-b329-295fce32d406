#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本，用于调试问题
"""

import sys
import traceback

def test_import():
    """测试导入"""
    try:
        print("🔧 测试导入模块...")
        from batch_readnum_spider import BatchReadnumSpider
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        return False

def test_create_instance():
    """测试创建实例"""
    try:
        print("🔧 测试创建实例...")
        from batch_readnum_spider import BatchReadnumSpider
        spider = BatchReadnumSpider()
        print("✅ 实例创建成功")
        return spider
    except Exception as e:
        print(f"❌ 实例创建失败: {e}")
        traceback.print_exc()
        return None

def test_load_auth():
    """测试加载认证信息"""
    try:
        print("🔧 测试加载认证信息...")
        spider = test_create_instance()
        if not spider:
            return False
        
        result = spider.load_auth_info()
        if result:
            print("✅ 认证信息加载成功")
            print(f"   __biz: {spider.biz}")
            print(f"   appmsg_token: {spider.appmsg_token[:20] if spider.appmsg_token else 'None'}...")
            return True
        else:
            print("❌ 认证信息加载失败")
            return False
    except Exception as e:
        print(f"❌ 测试认证信息加载失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始简单测试")
    print("="*50)
    
    # 测试导入
    if not test_import():
        return
    
    # 测试创建实例
    if not test_create_instance():
        return
    
    # 测试加载认证信息
    if not test_load_auth():
        return
    
    print("\n✅ 所有基础测试通过！")

if __name__ == "__main__":
    main()
