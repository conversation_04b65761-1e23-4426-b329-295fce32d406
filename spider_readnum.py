import re
import requests
import json
import winreg
import ctypes
import contextlib

# --- 代理管理 ---
INTERNET_OPTION_SETTINGS_CHANGED = 39
INTERNET_OPTION_REFRESH = 37
FWinHttpSetDefaultProxyConfiguration = ctypes.windll.winhttp.WinHttpSetDefaultProxyConfiguration
InternetSetOption = ctypes.windll.wininet.InternetSetOptionW

@contextlib.contextmanager
def manage_system_proxy(proxy_address="127.0.0.1:8080"):
    """
    一个上下文管理器，用于在代码块执行期间临时禁用指定的系统代理。
    """
    original_state = {"enabled": False, "server": ""}
    was_active = False
    key = None
    try:
        # 打开注册表项
        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Microsoft\Windows\CurrentVersion\Internet Settings", 0, winreg.KEY_READ | winreg.KEY_WRITE)
        
        # 读取原始代理状态
        try:
            original_state["enabled"] = winreg.QueryValueEx(key, "ProxyEnable")[0] == 1
            original_state["server"] = winreg.QueryValueEx(key, "ProxyServer")[0]
        except FileNotFoundError:
            # 如果值不存在，则代理是禁用的
            pass

        # 检查代理是否是我们需要禁用的那个
        if original_state["enabled"] and original_state["server"] == proxy_address:
            was_active = True
            print(f"检测到活动代理 {proxy_address}，正在临时禁用...")
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 0)
            # 通知系统设置已更改
            InternetSetOption(0, INTERNET_OPTION_SETTINGS_CHANGED, 0, 0)
            InternetSetOption(0, INTERNET_OPTION_REFRESH, 0, 0)
        
        yield # 执行主代码块

    finally:
        # 恢复原始代理设置
        if was_active and key:
            print(f"正在恢复代理 {proxy_address}...")
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 1)
            InternetSetOption(0, INTERNET_OPTION_SETTINGS_CHANGED, 0, 0)
            InternetSetOption(0, INTERNET_OPTION_REFRESH, 0, 0)
        if key:
            winreg.CloseKey(key)


url = "https://mp.weixin.qq.com/s"

params = {
            #@公众号文章信息组成
    "__biz": [
                "MjM5MTczODg0MA=="
            ],
    "mid": [
                "2649920127"
            ],
    "idx": [
                "1"
            ],
    "sn": [
                "d2819d0024fbbc0b7475929235ef5529"
            ],
            # 获取阅读量
    "pass_ticket": [
        "bAigYU+QPM9xO9fBI0xeJkYOUP95u9RE75zAChNaKgDGBR8zB7FA2dmVwVc5HOx8"
    ],
    "wx_header": [
        "1"
    ],
    # 获取内容
    "chksm": [
        "be"
    ],
}

headers = {
    'cache-control': 'max-age=0',
    'x-wechat-key': 'daf9bdc5abc4e8d0633000b7e7fd72f2923440561fa6cc03d009d521979e35cc27f341291190ec2ca2bb6ab7ddd3c7440276082737efcdeda665941b66f8e6ca8b89b70478fcadab515e6e623beb1612d8ed749609a709e8fe1233f2382345b134e53e2456bc1f5509ad65fa3792b93d79bc39c5247f7fb67a56f564315c7bb0',
    'x-wechat-uin': 'Mjc3NDgzMTgyNQ%3D%3D',
    'exportkey': 'n_ChQIAhIQohZwKaZK3iZEuk5Yjeu0exLmAQIE97dBBAEAAAAAAHkFIjzHB2QAAAAOpnltbLcz9gKNyK89dVj0NTHOI8SZgXVLspA72qObk0B8kQpsa27SKHs4vhCJyeJ%2FjaD3UlAQ5OYxoPhZ%2BugrgjAsIM%2BSQ0C405uqxahRfDI4tmSG3lra82SAG3qpqVe6RYRg7nq6FRHCCEE0o2AkqFVeqAhPJbrglDvwOPNACxRiYZfwxla3e%2BZ2jFfUTzayLnu0wx4UKOcx5RocKVSbXUd%2B9QGcCHDqA1oI1ypXsnKcx%2BJhYrlxWjajDxTPtZZbH6BvPzUi5QTnwK4MKH7P',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c37) XWEB/14315 Flue',
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/wxpic,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-dest': 'document',
    'accept-encoding': 'gzip, deflate, br',
    'accept-language': 'zh-CN,zh;q=0.9',
    'priority': 'u=0, i',
}

payload = {}

# 使用上下文管理器来自动处理代理
with manage_system_proxy("127.0.0.1:8080"):
    print("正在发送网络请求...")
    response = requests.request("GET", url, params=params, headers=headers, data=payload)

    html_content = response.text

    # 保存原始的响应结果
    with open('raw_response.html', 'w', encoding='utf-8') as f:
        f.write(html_content)

    # 使用正则表达式提取文章标题、作者和内容
    title_match = re.search(r'<meta property="og:title" content="(.*?)"', html_content)
    author_match = re.search(r'<meta property="og:article:author" content="(.*?)"', html_content)
    content_match = re.search(r'id="js_content".*?>(.*?)</div>', html_content, re.S)

    title = title_match.group(1) if title_match else "未找到标题"
    author = author_match.group(1) if author_match else "未找到作者"
    # 简单清理一下HTML标签
    content = re.sub(r'<.*?>', '', content_match.group(1)) if content_match else "未找到内容"

    # 提取阅读数、点赞数、在看数和分享数
    read_num_match = re.search(r"var cgiData = {[^}]*?read_num: '(\d+)'", html_content)
    like_num_match = re.search(r"window.appmsg_bar_data = {[^}]*?like_count: '(\d+)'", html_content)
    old_like_num_match = re.search(r"window.appmsg_bar_data = {[^}]*?old_like_count: '(\d+)'", html_content)
    share_count_match = re.search(r"window.appmsg_bar_data = {[^}]*?share_count: '(\d+)'", html_content)


    read_count = int(read_num_match.group(1)) if read_num_match else 0
    like_count = int(like_num_match.group(1)) if like_num_match else 0
    old_like_count = int(old_like_num_match.group(1)) if old_like_num_match else 0
    share_count = int(share_count_match.group(1)) if share_count_match else 0


    # 组织成字典
    article_data = {
        "title": title.strip(),
        "author": author.strip(),
        "content": content.strip(),
        "read_count": read_count,
        "like_count": like_count,
        "old_like_count": old_like_count,
        "share_count": share_count
    }

    # 将字典保存为 JSON 文件
    with open('article.json', 'w', encoding='utf-8') as f:
        json.dump(article_data, f, ensure_ascii=False, indent=4)

print("文章数据已成功保存到 article.json 文件中。")
print("原始响应已保存到 raw_response.html 文件中。")