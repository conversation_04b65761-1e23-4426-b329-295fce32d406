#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量阅读量抓取器的x-wechat-key实现
"""

from batch_readnum_spider import Batch<PERSON><PERSON><PERSON>Spider

def test_headers_initialization():
    """测试headers初始化是否包含x-wechat-key"""
    print("🧪 测试headers初始化...")
    
    spider = BatchReadnumSpider()
    
    # 检查默认headers是否包含x-wechat-key
    if 'x-wechat-key' in spider.headers:
        print(f"✅ 默认headers包含x-wechat-key: {spider.headers['x-wechat-key'][:20]}...")
        print(f"🔍 完整的x-wechat-key长度: {len(spider.headers['x-wechat-key'])} 字符")
    else:
        print("❌ 默认headers缺少x-wechat-key")
    
    # 显示所有headers
    print(f"📋 所有headers: {list(spider.headers.keys())}")
    
    return spider

def test_single_article_extraction():
    """测试单篇文章的统计数据提取"""
    print("\n🧪 测试单篇文章统计数据提取...")
    
    spider = BatchReadnumSpider()
    
    # 使用一个示例URL（需要替换为实际的文章URL）
    test_url = "https://mp.weixin.qq.com/s?__biz=MjM5MTczODg0MA==&mid=2649920127&idx=1&sn=d2819d0024fbbc0b7475929235ef5529&chksm=be"
    
    print(f"🔍 测试URL: {test_url}")
    
    # 尝试提取统计数据
    try:
        result = spider.extract_article_content_and_stats(test_url)
        if result:
            print("✅ 成功提取统计数据:")
            print(f"   标题: {result.get('title', 'N/A')}")
            print(f"   阅读量: {result.get('read_count', 0)}")
            print(f"   点赞数: {result.get('like_count', 0)}")
            print(f"   分享数: {result.get('share_count', 0)}")
        else:
            print("❌ 未能提取统计数据")
    except Exception as e:
        print(f"❌ 提取过程出错: {e}")

def main():
    """主测试函数"""
    print("🚀 批量阅读量抓取器测试")
    print("="*50)
    
    # 测试1: headers初始化
    spider = test_headers_initialization()
    
    # 测试2: 单篇文章提取（可选，需要有效的文章URL）
    # test_single_article_extraction()
    
    print("\n✅ 测试完成")
    print("\n💡 主要改进:")
    print("   1. 在默认headers中添加了x-wechat-key参数")
    print("   2. 改进了load_auth_info方法，确保x-wechat-key的正确处理")
    print("   3. 在extract_article_content_and_stats中添加了x-wechat-key验证")
    print("\n🔧 使用说明:")
    print("   - x-wechat-key是获取阅读量数据的关键参数")
    print("   - 如果cookie文件中有更新的x-wechat-key，会自动使用")
    print("   - 如果没有，会使用默认值（来自spider_readnum.py的成功实现）")

if __name__ == "__main__":
    main()
