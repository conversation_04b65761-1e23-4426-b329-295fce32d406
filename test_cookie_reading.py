#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试cookie读取和x-wechat-key使用
"""

from read_cookie import ReadCookie
from batch_readnum_spider import BatchReadnumSpider

def test_cookie_reading():
    """测试cookie读取功能"""
    print("🧪 测试cookie读取功能...")
    
    cookie_reader = ReadCookie()
    result = cookie_reader.get_latest_cookies()
    
    if result:
        print("✅ Cookie读取成功:")
        print(f"   __biz: {result['biz']}")
        print(f"   appmsg_token: {result['appmsg_token'][:20]}...")
        print(f"   cookie长度: {len(result['cookie_str'])}")
        print(f"   headers数量: {len(result['headers'])}")
        
        # 检查关键headers
        headers = result['headers']
        key_headers = ['x-wechat-key', 'x-wechat-uin', 'exportkey']
        
        print("\n🔍 关键headers检查:")
        for header in key_headers:
            if header in headers:
                value = headers[header]
                print(f"   ✅ {header}: {value[:20]}... (长度: {len(value)})")
            else:
                print(f"   ❌ 缺少 {header}")
        
        return result
    else:
        print("❌ Cookie读取失败")
        return None

def test_spider_initialization():
    """测试爬虫初始化"""
    print("\n🧪 测试爬虫初始化...")
    
    spider = BatchReadnumSpider()
    
    # 检查默认headers
    print("📋 默认headers:")
    if 'x-wechat-key' in spider.headers:
        print(f"   ✅ x-wechat-key: {spider.headers['x-wechat-key'][:20]}...")
    else:
        print("   ❌ 缺少x-wechat-key")
    
    # 测试load_auth_info
    print("\n🔄 测试认证信息加载...")
    success = spider.load_auth_info()
    
    if success:
        print("✅ 认证信息加载成功")
        # 再次检查headers是否被正确更新
        print("\n🔍 更新后的headers:")
        if 'x-wechat-key' in spider.headers:
            print(f"   ✅ x-wechat-key: {spider.headers['x-wechat-key'][:20]}...")
        else:
            print("   ❌ 缺少x-wechat-key")
    else:
        print("❌ 认证信息加载失败")
    
    return spider

def compare_keys():
    """比较不同来源的x-wechat-key"""
    print("\n🧪 比较x-wechat-key来源...")
    
    # 从cookie文件读取
    cookie_reader = ReadCookie()
    cookie_result = cookie_reader.get_latest_cookies()
    
    # 从爬虫实例读取
    spider = BatchReadnumSpider()
    
    if cookie_result and 'x-wechat-key' in cookie_result['headers']:
        cookie_key = cookie_result['headers']['x-wechat-key']
        spider_key = spider.headers.get('x-wechat-key', '')
        
        print(f"📄 Cookie文件中的key: {cookie_key[:20]}...")
        print(f"🕷️ 爬虫默认key:     {spider_key[:20]}...")
        
        if cookie_key == spider_key:
            print("✅ 两个key完全一致")
        else:
            print("❌ 两个key不一致")
            print("💡 建议：应该优先使用cookie文件中的最新key")
            
            # 显示差异的位置
            for i, (c1, c2) in enumerate(zip(cookie_key, spider_key)):
                if c1 != c2:
                    print(f"   差异开始位置: {i}")
                    print(f"   Cookie: ...{cookie_key[max(0, i-10):i+10]}...")
                    print(f"   Spider: ...{spider_key[max(0, i-10):i+10]}...")
                    break
    else:
        print("❌ 无法从cookie文件获取x-wechat-key")

def main():
    """主测试函数"""
    print("🚀 Cookie读取和x-wechat-key测试")
    print("="*60)
    
    # 测试1: Cookie读取
    cookie_result = test_cookie_reading()
    
    # 测试2: 爬虫初始化
    spider = test_spider_initialization()
    
    # 测试3: Key比较
    compare_keys()
    
    print("\n" + "="*60)
    print("📊 测试总结:")
    
    if cookie_result:
        print("✅ Cookie文件读取正常")
        if 'x-wechat-key' in cookie_result['headers']:
            print("✅ Cookie文件包含x-wechat-key")
        else:
            print("❌ Cookie文件缺少x-wechat-key")
    else:
        print("❌ Cookie文件读取失败")
    
    print("\n💡 建议:")
    print("1. 确保wechat_keys.txt文件包含最新的x-wechat-key")
    print("2. 爬虫会优先使用cookie文件中的key，如果没有则使用默认值")
    print("3. 如果仍然无法获取阅读量，可能需要重新抓取cookie")

if __name__ == "__main__":
    main()
