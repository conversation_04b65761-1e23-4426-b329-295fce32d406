# coding:utf-8
# wxCrawler.py
import os
import requests
import json
import urllib3
import utils


class WxCrawler(object):
	"""翻页内容抓取"""
	urllib3.disable_warnings()

	def __init__(self, appmsg_token, biz, cookie, begin_page_index=0, end_page_index=100):
		# 起始页数
		self.begin_page_index = begin_page_index
		# 结束页数
		self.end_page_index = end_page_index
		# 抓了多少条了
		self.num = 1

		self.appmsg_token = appmsg_token
		self.biz = biz
		self.headers = {
			"User-Agent": "Mozilla/5.0 AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.132 MQQBrowser/6.2 Mobile",
			"Cookie": cookie
		}
		self.cookie = cookie

	def article_list(self, context):
		articles = json.loads(context).get('general_msg_list')
		return json.loads(articles)

	def run(self):
		# 翻页地址 - 修复URL中的空格
		page_url = "https://mp.weixin.qq.com/mp/profile_ext?action=getmsg&__biz={}&f=json&offset={}&count=10&is_ok=1&scene=&uin=777&key=777&pass_ticket={}&wxtoken=&appmsg_token={}&x5=0&f=json"
		
		# 将 cookie 字符串清理并字典化
		clean_cookie = self.cookie.replace('\u00a0', ' ').strip()
		wx_dict = utils.str_to_dict(clean_cookie, join_symbol='; ', split_symbol='=')
		
		# 请求地址
		response = requests.get(
			page_url.format(
				self.biz, 
				self.begin_page_index * 10, 
				wx_dict['pass_ticket'], 
				self.appmsg_token
			), 
			headers=self.headers, 
			verify=False
		)
		
		# 将文章列表字典化
		articles = self.article_list(response.text)

		for a in articles['list']:
			# 公众号中主条
			if 'app_msg_ext_info' in a.keys() and '' != a.get('app_msg_ext_info').get('content_url',''):
				print(str(self.num) + "条", a.get('app_msg_ext_info').get('title'), a.get('app_msg_ext_info').get('content_url'))
			# 公众号中副条
			if 'app_msg_ext_info' in a.keys():
				for m in a.get('app_msg_ext_info').get('multi_app_msg_item_list',[]):
					print(str(self.num) + "条", m.get('title'), m.get('content_url'))  # 修复这里应该是m.get

			self.num = self.num + 1

		self.is_exit_or_continue()
		# 递归调用
		self.run()

	def is_exit_or_continue(self):
		self.begin_page_index = self.begin_page_index + 1

		if self.begin_page_index > self.end_page_index:
			os._exit(0)  # 修复退出方法
